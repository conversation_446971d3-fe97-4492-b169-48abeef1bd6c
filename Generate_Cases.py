import time
import os
from process_data import *
from find_cases_based_on_fittingdata import *
from find_cases_based_on_bsetoffset import *


def main():
    """Main function for data processing workflow

    Args:
        output_dir: Output directory, default is 'output'
    """
    start_time = time.time()
    print("Starting data processing...")

    try:
        # Ensure output directory exists
        input_dir = './'
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. Load data
        print("1. Loading data...")
        data = load_data(input_dir)
        if data.empty:
            print("Failed to load valid data")
            return

        # 2. Data preprocessing
        print("2. Preprocessing data...")
        data = preprocess_data(data)

        # 3. Process fitting_data (vt_list/fail_list) - using optimized version
        print("3. Processing fitting_data...")
        fitting_data_final = process_fitting_data_with_options(data, use_optimized=True)

        exit()
        # Generate offset cases
        generate_offset_cases(fitting_data_final)
        
        exit()
        
        # 4. Process read_data (FAILS4CHUNK)
        print("4. Processing read_data...")
        process_read_data(data)

        # 5. Process bestoffset (BestDAC)
        print("5. Processing bestoffset...")
        bestoffset = process_bestoffset(data)
        # print("  ...Performing cluster analysis...")
        # perform_kmeans_cluster_analysis(bestoffset, n_clusters=5)

        # Processing complete
        elapsed_time = time.time() - start_time
        print(f"Processing complete, total time: {elapsed_time:.2f} seconds")

    except Exception as e:
        print(f'Main process exception: {e}')
        elapsed_time = time.time() - start_time
        print(f"Processing terminated abnormally, runtime: {elapsed_time:.2f} seconds")

if __name__ == '__main__':
    main()
