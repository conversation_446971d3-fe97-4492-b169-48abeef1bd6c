import numpy as np
import matplotlib.pyplot as plt
import os
from sklearn.cluster import KMeans
from process_data import save_results_to_csv

def perform_kmeans_cluster_analysis(bestoffset, n_clusters=10):
    """Perform KMeans clustering analysis on bestoffset data
    Reconstruct clustering analysis process to reduce code duplication
    """

    # Check if bestoffset is empty
    if bestoffset.empty:
        print("bestoffset is empty, cannot perform cluster analysis")
        return None

    # Check if bestoffset is missing Region column
    if 'Region' not in bestoffset.columns:
        print("bestoffset is missing Region column, cannot perform cluster analysis")
        return None

    # Define page types and corresponding state columns
    page_configs = {
        'LP': {
            'states': ['State1', 'State5'],
            'is_3d': False
        },
        'MP': {
            'states': ['State2', 'State4', 'State6'],
            'is_3d': True
        },
        'UP': {
            'states': ['State3', 'State7'],
            'is_3d': False
        }
    }

    # Check if necessary columns exist
    all_states = [state for config in page_configs.values() for state in config['states']]
    missing_states = [state for state in all_states if state not in bestoffset.columns]
    if missing_states:
        print(f"Missing the following state columns: {missing_states}")

    # Group data by Region
    regions = bestoffset['Region'].unique()

    # Create results list
    cluster_results = []

    # Perform clustering for each page type and region
    for page_type, config in page_configs.items():
        states = config['states']
        is_3d = config['is_3d']

        # Check if required state columns exist
        if not all(state in bestoffset.columns for state in states):
            print(f"Missing state columns for {page_type}, skipping this page type")
            continue

        # Perform clustering for each region
        for region in regions:
            # Filter data for current region
            region_data = bestoffset[bestoffset['Region'] == region]

            # Extract vector data
            vectors = region_data[states].values

            # Filter out rows containing NaN
            vectors = vectors[~np.isnan(vectors).any(axis=1)]

            # Skip if insufficient data
            if len(vectors) < 10:
                print(f"Region {region} {page_type} has insufficient data, skipping clustering")
                continue

            # Perform KMeans clustering
            n_clusters_actual = min(n_clusters, len(vectors))  # Ensure number of clusters doesn't exceed data points
            kmeans = KMeans(n_clusters=n_clusters_actual, random_state=0, n_init='auto').fit(vectors)

            # Get cluster centers and convert to integers
            cluster_centers = np.round(kmeans.cluster_centers_).astype(int)

            # Add to results list
            cluster_results.append({
                'Region': region,
                'Centers': cluster_centers,
                'Method': 'KMeans',
                'Page': page_type
            })

            # Visualize clustering results
            if is_3d:  # 3D plot
                fig = plt.figure(figsize=(10, 8))
                ax = fig.add_subplot(111, projection='3d')
                ax.scatter(vectors[:, 0], vectors[:, 1], vectors[:, 2], c=kmeans.labels_, cmap='viridis', marker='o', label='Data Points')
                ax.scatter(cluster_centers[:, 0], cluster_centers[:, 1], cluster_centers[:, 2], c='red', marker='X', s=200, label='Cluster Centers')
                ax.set_title(f'K-Means Clustering for Region: {region} ({page_type})')
                ax.set_xlabel(states[0])
                ax.set_ylabel(states[1])
                ax.set_zlabel(states[2])
                plt.legend()
            else:  # 2D plot
                plt.figure(figsize=(8, 6))
                plt.scatter(vectors[:, 0], vectors[:, 1], c=kmeans.labels_, cmap='viridis', marker='o', label='Data Points')
                plt.scatter(cluster_centers[:, 0], cluster_centers[:, 1], c='red', marker='X', s=200, label='Cluster Centers')
                plt.title(f'K-Means Clustering for Region: {region} ({page_type})')
                plt.xlabel(states[0])
                plt.ylabel(states[1])
                plt.legend()
                plt.grid()

            # Save image - use os.path.join to ensure cross-platform compatibility
            output_path = os.path.join('output', f'KMeans_Clustering_{page_type}_Region_{region}.png')
            plt.savefig(output_path)
            plt.close()  # Close the figure to free memory

    print(f"Cluster analysis complete, {len(cluster_results)} cluster centers for regions")

    # Save clustering results
    save_results_to_csv(cluster_results, 'cluster_results', output_dir='output')