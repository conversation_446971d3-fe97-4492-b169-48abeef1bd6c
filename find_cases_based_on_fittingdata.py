import pandas as pd
import numpy as np
import itertools
from device_configure import device_name, FBC_criterion
from process_data import save_results_to_csv

def get_wlmode_page_state_map(wlmode, device_name):
    """
    Get page_state_map based on WLmode and device_name
    
    Args:
        wlmode (str): WL mode ('QLC', 'TLC', 'SLC')
        device_name (str): Device name
    
    Returns:
        dict: Mapping from page_type to state column list
    """
    if wlmode == 'QLC':
        return {
            'LP': ['State1', 'State7', 'State13'],
            'MP': ['State2', 'State6', 'State8', 'State12'],
            'UP': ['State4', 'State9', 'State11', 'State14'],
            'XP': ['State0', 'State3', 'State5', 'State10']
        }
    elif wlmode == 'TLC':
        if device_name == 'BiCS5':
            return {
                'LP': ['State1', 'State4', 'State6'],
                'MP': ['State0', 'State2', 'State5'],
                'UP': ['State3']
            }
        else:
            return {
                'LP': ['State0', 'State4'],
                'MP': ['State1', 'State3', 'State5'],
                'UP': ['State2', 'State6']
            }
    elif wlmode == 'SLC':
        return {
            'LP': ['State0']
        }
    else:
        raise ValueError(f"Unsupported WLmode: {wlmode}")

def validate_state_columns(df, state_cols):
    """
    Validate if DataFrame contains required State columns
    
    Args:
        df (pd.DataFrame): Input dataframe
        state_cols (list): List of required State columns
    
    Returns:
        bool: Whether all required columns are present
    """
    return all(col in df.columns for col in state_cols)

def build_state_offset_mapping(region_df, state_cols, fbc_threshold=None):
    """
    Build state-offset-wl-fbc mapping
    
    Args:
        region_df (pd.DataFrame): Regional dataframe
        state_cols (list): List of State columns
        fbc_threshold (int, optional): FBC threshold. Defaults to FBC_criterion.
    
    Returns:
        dict: {state: {offset: {wl_key: fbc}}}
    """
    if fbc_threshold is None:
        fbc_threshold = FBC_criterion
    
    state_offset_wl_fbc = {state: {} for state in state_cols}
    
    for state in state_cols:
        state_df = region_df[["Channel", "Ce", "Lun", "Block", "WordLine", "Offset", state]].dropna()
        
        for offset in state_df["Offset"].unique():
            offset = int(offset)
            # Build key_fbc dictionary: keys are WL Keys, values are FBC for each WL Key at current state and offset
            key_fbc = state_df[state_df["Offset"] == offset].set_index(
                ["Channel", "Ce", "Lun", "Block", "WordLine"]
            )[state].to_dict()
            
            # Only keep WLs with FBC <= threshold, offset is kept if at least some WLs meet the criteria
            filtered_key_fbc = {
                k: fbc for k, fbc in key_fbc.items() 
                if pd.notna(fbc) and fbc <= fbc_threshold
            }
            
            if filtered_key_fbc:
                state_offset_wl_fbc[state][offset] = filtered_key_fbc
    
    return state_offset_wl_fbc

def process_offset_combinations(combo_cover, combo_stats, all_keys, group_info):
    """
    Process offset combinations for three scenarios
    
    Args:
        combo_cover (list): Combination coverage information
        combo_stats (list): Combination statistics information
        all_keys (set): All WL keys
        group_info (dict): Group information dictionary
    
    Returns:
        list: Results list
    """
    results = []
    max_cover = len(all_keys)
    
    # Scenario 1: Single combination full coverage
    full_cover_combos = [stat for stat in combo_stats if stat['NumCoveredWL'] == max_cover]
    if full_cover_combos:
        full_cover_combos.sort(key=lambda x: (
            x['FBC_sum_max'] if x['FBC_sum_max'] is not None else float('inf'),
            x['FBC_sum_p90'] if x['FBC_sum_p90'] is not None else float('inf'),
            x['FBC_sum_mean'] if x['FBC_sum_mean'] is not None else float('inf')
        ))
        
        print(f"Group {group_info} has {len(full_cover_combos)} single combination full coverage solutions")
        for stat in full_cover_combos:
            results.append({
                **group_info,
                'Scenario': 'Single Combo Full Coverage',
                **stat
            })
        return results

    # Scenario 2: Multi-combination coverage (using beam search)
    print(f"Group {group_info} starting Pareto front + beam search analysis...")
    beam_solutions = beam_search_solutions(combo_cover, combo_stats, all_keys, beam_width=5, max_solutions=20)

    if beam_solutions:
        print(f"Group {group_info} found {len(beam_solutions)} multi-combination coverage solutions")
        for solution_idx, solution in enumerate(beam_solutions):
            for combo_idx, stat in enumerate(solution['stats']):
                stat['SolutionID'] = solution_idx + 1
                stat['ComboIndex'] = combo_idx + 1
                stat['TotalCombosInSolution'] = solution['num_combos']
                results.append({
                    **group_info,
                    'Scenario': 'Multi-Combo Coverage Solution',
                    **stat
                })
        return results

    # Scenario 3: Minimum risk combinations
    if combo_stats:
        sorted_stats = sorted(combo_stats, key=lambda x: (
            x['FBC_sum_max'] if x['FBC_sum_max'] is not None else float('inf'),
            x['FBC_sum_p90'] if x['FBC_sum_p90'] is not None else float('inf'),
            x['FBC_sum_mean'] if x['FBC_sum_mean'] is not None else float('inf'),
            -x['NumCoveredWL']  # More coverage is better (negative for descending)
        ))

        print(f"Group {group_info} cannot fully cover all WLs, outputting top 20 minimum risk combinations")
        for stat in sorted_stats[:20]:
            results.append({
                **group_info,
                'Scenario': 'Minimum Risk Combination',
                **stat
            })
    
    return results

def generate_offset_cases(fitting_data_final):
    """
    New implementation of generate_offset_cases
    Process data by hierarchical grouping: WLmode->WLType->WLGroup->PageType
    
    Args:
        fitting_data_final (pd.DataFrame): Processed fitting_data dataframe
    
    Returns:
        None: Results are saved to CSV file
    """
    if fitting_data_final is None or fitting_data_final.empty:
        print("fitting_data_final is empty, cannot generate offset cases")
        return None

    print("Starting to generate offset cases with new hierarchical grouping structure...")
    results = []

    # Level 1: Group by WLmode(QLC/TLC/SLC)
    for wlmode, wlmode_df in fitting_data_final.groupby('WLmode'):
        print(f"\nProcessing WLmode: {wlmode}")

        # Get page_state_map for current WLmode
        try:
            page_state_map = get_wlmode_page_state_map(wlmode, device_name)
        except ValueError as e:
            print(f"Skipping unsupported WLmode {wlmode}: {e}")
            continue
        
        # Level 2: Group by WLType（Inner/Edge/Close）
        for wltype, wltype_df in wlmode_df.groupby('WLType'):
            print(f"  Processing WLType: {wltype}")

            # Level 3: Decide whether to group by WLGroup based on WLmode
            if wlmode == 'QLC':
                # QLC needs to be grouped by WLGroup
                group_iterator = wltype_df.groupby('WLGroup')
                group_name = 'WLGroup'
            else:
                # TLC and SLC process directly, create virtual grouping
                group_iterator = [('ALL', wltype_df)]
                group_name = 'WLGroup'

            for group_value, group_df in group_iterator:
                if wlmode == 'QLC':
                    print(f"    Processing WLGroup: {group_value}")
                else:
                    print(f"    Processing {wlmode} data (no WLGroup grouping)")
                
                # Level 4: Group by PageType
                for page_type, state_cols in page_state_map.items():
                    print(f"      Processing PageType: {page_type}, StateCols: {state_cols}")

                    # Validate if State columns exist
                    if not validate_state_columns(group_df, state_cols):
                        print(f"        Skipping: missing required State columns")
                        continue
                    
                    # Build group information
                    group_info = {
                        'WLmode': wlmode,
                        'WLType': wltype,
                        'PageType': page_type
                    }
                    if wlmode == 'QLC':
                        group_info['WLGroup'] = group_value
                    else:
                        group_info['WLGroup'] = 'N/A'
                    
                    # Build state-offset-wl-fbc mapping
                    state_offset_wl_fbc = build_state_offset_mapping(group_df, state_cols)
                    
                    # Generate offset lists
                    offset_lists = [
                        [int(o) for o in state_offset_wl_fbc[state].keys()] 
                        for state in state_cols
                    ]
                    
                    if not all(offset_lists):
                        print(f"        Skipping: some States have no valid offsets")
                        continue

                    # Generate all offset combinations
                    offset_combinations = list(itertools.product(*offset_lists))
                    print(f"        {page_type} offset combinations count: {len(offset_combinations)}")

                    if not offset_combinations:
                        continue

                    # Get all WL keys
                    all_keys = set(tuple(row) for row in group_df[
                        ["Channel", "Ce", "Lun", "Block", "WordLine"]
                    ].values)

                    # Generate combination coverage and statistics
                    combo_cover, combo_stats = generate_combo_statistics(
                        offset_combinations, state_offset_wl_fbc, state_cols, all_keys
                    )

                    if not combo_stats:
                        print(f"        Skipping: no valid combination statistics")
                        continue
                    
                    # Process offset combinations for three scenarios
                    group_results = process_offset_combinations(
                        combo_cover, combo_stats, all_keys, group_info
                    )
                    
                    results.extend(group_results)
    
    # Output results
    if results:
        df = pd.DataFrame(results)
        print(f"\nTotal generated {len(df)} offset combination records")
        save_results_to_csv(df, 'offset_cases_new', output_dir='output')

        # Output grouping statistics
        print("\nGrouping statistics:")
        summary = df.groupby(['WLmode', 'WLType', 'WLGroup', 'PageType']).size().reset_index(name='Count')
        print(summary.to_string(index=False))
    else:
        print("No offset combinations found that meet the conditions")

def generate_combo_statistics(offset_combinations, state_offset_wl_fbc, state_cols, all_keys):
    """
    Generate combination coverage and statistics
    
    Args:
        offset_combinations (list): List of offset combinations
        state_offset_wl_fbc (dict): State-offset-wl-fbc mapping
        state_cols (list): List of State columns
        all_keys (set): Set of all WL keys
    
    Returns:
        tuple: (combo_cover, combo_stats)
    """
    combo_cover = []
    combo_stats = []
    
    for combo in offset_combinations:
        combo = tuple(int(o) for o in combo)
        covered_keys = []
        fbc_sum_list = []
        state_fbc_lists = [[] for _ in state_cols]
        
        for key in all_keys:
            fbc_sum = 0
            valid = True
            fbc_states = []
            
            for idx, state in enumerate(state_cols):
                offset = int(combo[idx])
                fbc = state_offset_wl_fbc[state][offset].get(key, np.nan)
                
                if pd.isna(fbc):
                    valid = False
                    break
                
                fbc_sum += fbc
                fbc_states.append(fbc)
            
            # Use FBC_criterion instead of hardcoded 200
            if valid and fbc_sum <= FBC_criterion:
                covered_keys.append(key)
                fbc_sum_list.append(fbc_sum)
                for idx, fbc in enumerate(fbc_states):
                    state_fbc_lists[idx].append(fbc)
        
        combo_cover.append((combo, set(covered_keys)))
        
        # Generate statistics
        fbc_sum_mean = round(float(np.mean(fbc_sum_list)), 1) if fbc_sum_list else None
        fbc_sum_max = int(np.max(fbc_sum_list)) if fbc_sum_list else None
        fbc_sum_p90 = int(np.percentile(fbc_sum_list, 90)) if fbc_sum_list else None
        coverage = (len(covered_keys) / len(all_keys) * 100) if all_keys else 0
        
        # Risk level classification
        risk_level, risk_color = classify_risk_by_max_fbc(fbc_sum_max)
        
        combo_stat = {
            'OffsetCombo': combo,
            'NumCoveredWL': len(covered_keys),
            'Coverage': f'{coverage:.2f}%',
            'FBC_sum_mean': fbc_sum_mean,
            'FBC_sum_max': fbc_sum_max,
            'FBC_sum_p90': fbc_sum_p90,
            'RiskLevel': risk_level,
            'RiskColor': risk_color
        }
        
        # Add statistics for each State
        for idx, state in enumerate(state_cols):
            state_fbc = state_fbc_lists[idx]
            combo_stat[f'{state}_mean'] = round(float(np.mean(state_fbc)), 1) if state_fbc else None
            combo_stat[f'{state}_max'] = int(np.max(state_fbc)) if state_fbc else None
            combo_stat[f'{state}_p90'] = int(np.percentile(state_fbc, 90)) if state_fbc else None
            combo_stat[f'{state}_count'] = len(state_fbc)
        
        combo_stats.append(combo_stat)
    
    return combo_cover, combo_stats

def classify_risk_by_max_fbc(fbc_max):
    """
    Classify risk level based on FBC maximum value
    
    Args:
        fbc_max (int or None): Maximum FBC value
    
    Returns:
        tuple: (risk_level, risk_color)
    """
    if fbc_max is None:
        return "Unknown Risk", "gray"
    if fbc_max <= FBC_criterion - 100:
        return "Low Risk", "green"
    elif fbc_max <= FBC_criterion - 50:
        return "Medium Risk", "yellow"
    elif fbc_max <= FBC_criterion:
        return "High Risk", "orange"
    else:
        return "Very High Risk", "red"

def get_pareto_front(combo_cover, combo_stats, uncovered, beam_width=5):
    """
    Pareto front filtering based on FBC maximum value
    
    Args:
        combo_cover (list): List of (combo, cover_set) tuples
        combo_stats (list): List of combination statistics
        uncovered (set): Set of uncovered WL keys
        beam_width (int): Beam width for search
    
    Returns:
        list: Pareto front candidates
    """
    candidates = []
    
    for i, (combo, cover_set) in enumerate(combo_cover):
        new_covered = len(cover_set & uncovered)
        if new_covered > 0:  # Only consider combinations with new coverage
            stat = combo_stats[i]
            # Two objectives: total coverage (larger is better), FBC max (smaller is better)
            total_coverage = stat['NumCoveredWL']
            fbc_max_risk = stat['FBC_sum_max'] if stat['FBC_sum_max'] is not None else float('inf')
            candidates.append((combo, cover_set, stat, total_coverage, fbc_max_risk, new_covered, i))
    
    if not candidates:
        return []
    
    # Pareto front filtering
    pareto_front = []
    for candidate in candidates:
        is_dominated = False
        for other in candidates:
            # other dominates candidate: coverage >= and max FBC <=, at least one strictly better
            if (other[3] >= candidate[3] and other[4] <= candidate[4] and
                (other[3] > candidate[3] or other[4] < candidate[4])):
                is_dominated = True
                break
        
        if not is_dominated:
            pareto_front.append(candidate)
    
    # Sort by new coverage first, then by FBC max
    return sorted(pareto_front, key=lambda x: (-x[5], x[4]))[:beam_width]

def search_layer(current_paths, depth, combo_cover, combo_stats, all_keys, max_depth, beam_width, solutions):
    if depth >= max_depth:
        return []
    
    next_paths = []
    for path_info in current_paths:
        selected_combos, uncovered, used_indices, path_stats = path_info
        
        if not uncovered:  # Already fully covered, this is a solution
            solutions.append({
                'combos': selected_combos[:],
                'stats': path_stats[:],
                'total_coverage': len(all_keys),
                'num_combos': len(selected_combos)
            })
            continue
        
        # Get remaining combinations for the current path
        remaining_cover = [(combo_cover[i][0], combo_cover[i][1], combo_stats[i], i) 
                         for i in range(len(combo_cover)) 
                         if i not in used_indices]
        
        if not remaining_cover:
            continue
        
        # Convert format for Pareto filtering
        remaining_combo_cover = [(combo, cover_set) for combo, cover_set, _, _ in remaining_cover]
        remaining_combo_stats = [stat for _, _, stat, _ in remaining_cover]
        
        # Pareto front selection
        pareto_candidates = get_pareto_front(remaining_combo_cover, remaining_combo_stats, uncovered, beam_width)
        
        # Extend paths
        for combo, cover_set, stat, _, _, _, pareto_idx in pareto_candidates:
            new_covered = cover_set & uncovered
            if new_covered:
                # Find original index
                original_idx = remaining_cover[pareto_idx][3]
                # Update statistics
                updated_stat = stat.copy()
                updated_stat['CoveredWL'] = [list(k) for k in new_covered]
                
                new_path = (
                    selected_combos + [combo],
                    uncovered - new_covered,
                    used_indices | {original_idx},
                    path_stats + [updated_stat]
                )
                next_paths.append(new_path)
    
    # Beam search: keep the most promising paths
    # Sorting criteria: fewer uncovered WLs first, then by maximum FBC risk
    return sorted(next_paths, 
                 key=lambda x: (len(x[1]), 
                               max([s.get('FBC_sum_max', float('inf')) for s in x[3]] + [0])))[:beam_width * 2]

def beam_search_solutions(combo_cover, combo_stats, all_keys, beam_width=3, max_solutions=10, max_depth=5):
    """
    Beam search algorithm based on Pareto front for multi-solution finding
    
    Args:
        combo_cover (list): List of (combo, cover_set) tuples
        combo_stats (list): List of combination statistics
        all_keys (set): Set of all WL keys
        beam_width (int): Beam width for search
        max_solutions (int): Maximum number of solutions to find
        max_depth (int): Maximum depth for search
    
    Returns:
        list: List of solutions
    """
    # Initialize: start from the Pareto front
    initial_pareto = get_pareto_front(combo_cover, combo_stats, all_keys, beam_width)
    current_paths = []
    
    for combo, cover_set, stat, _, _, _, idx in initial_pareto:
        covered = cover_set & all_keys
        updated_stat = stat.copy()
        updated_stat['CoveredWL'] = [list(k) for k in covered]
        current_paths.append(([combo], all_keys - covered, {idx}, [updated_stat]))
    
    solutions = []
    
    # Layer-by-layer search
    for depth in range(max_depth - 1):
        current_paths = search_layer(current_paths, depth, combo_cover, combo_stats, all_keys, max_depth, beam_width, solutions)
        if len(solutions) >= max_solutions:
            break
    
    return solutions[:max_solutions]
