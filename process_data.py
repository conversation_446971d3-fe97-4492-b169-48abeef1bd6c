import os
import glob
import pandas as pd
import gc
import psutil
import numpy as np
from device_configure import *

def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def print_memory_info(step_name):
    """Print memory usage information for a specific step"""
    memory_mb = get_memory_usage()
    print(f"Memory usage after {step_name}: {memory_mb:.1f} MB")
    return memory_mb

def optimize_dataframe_dtypes(df):
    """Optimize DataFrame data types to reduce memory usage"""
    original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024

    for col in df.columns:
        col_type = df[col].dtype

        if col_type != 'object':
            continue

        # Check if column contains only numeric values (handle NaN values)
        if df[col].notna().any() and df[col].dropna().str.isnumeric().all():
            df[col] = pd.to_numeric(df[col], downcast='integer')
        else:
            # Use category for string columns with limited unique values
            unique_ratio = df[col].nunique() / len(df)
            if unique_ratio < 0.5:  # If less than 50% unique values
                df[col] = df[col].astype('category')

    optimized_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
    print(f"Memory optimization: {original_memory:.1f} MB -> {optimized_memory:.1f} MB "
          f"(saved {original_memory - optimized_memory:.1f} MB)")

    return df

def clear_memory():
    """Force garbage collection to free memory"""
    gc.collect()

def process_chunk_data(chunk_data):
    """Process a single chunk of fitting data with memory optimization"""
    if chunk_data.empty:
        return pd.DataFrame()

    try:
        # Define key columns for processing
        key_cols = ['Condition', 'Channel', 'Ce', 'Lun', 'Block', 'Page',
                   'WordLine', 'Level', 'WLmode', 'PEC', 'Partial_Prog',
                   'WLGroup', 'WLType', 'Measurement']

        # Process Value column efficiently
        # Split values and create expanded data in one step
        expanded_rows = []

        for _, row in chunk_data.iterrows():
            values = str(row['Value']).split('@')
            measurement = row['Measurement']

            # Create base row data
            base_data = {col: row[col] for col in key_cols if col != 'Measurement'}

            # Add each value as a separate row
            for i, value in enumerate(values):
                if value and value.strip():  # Skip empty values
                    row_data = base_data.copy()
                    row_data['index'] = i
                    row_data['Measurement'] = measurement
                    row_data['value'] = value.strip()
                    expanded_rows.append(row_data)

        if not expanded_rows:
            return pd.DataFrame()

        # Create DataFrame from expanded rows
        exploded_data = pd.DataFrame(expanded_rows)

        # Clear intermediate data
        del expanded_rows
        clear_memory()

        # Pivot to separate vt_list and fail_list columns
        pivot_data = exploded_data.pivot_table(
            index=[col for col in key_cols if col != 'Measurement'] + ['index'],
            columns='Measurement',
            values='value',
            aggfunc='first'
        ).reset_index()

        # Clear exploded data
        del exploded_data
        clear_memory()

        # Reset column names and convert to numeric
        pivot_data.columns.name = None
        for col in ['vt_list', 'fail_list']:
            if col in pivot_data.columns:
                pivot_data[col] = pd.to_numeric(pivot_data[col], errors='coerce')

        # Remove index column
        if 'index' in pivot_data.columns:
            pivot_data = pivot_data.drop(columns=['index'])

        # Final pivot by Condition to expand fail_list columns
        final_pivot = pivot_data.pivot_table(
            index=['Channel', 'Ce', 'Lun', 'Block', 'WordLine', 'WLmode',
                   'PEC', 'Partial_Prog', 'WLGroup', 'WLType', 'vt_list'],
            columns='Condition',
            values='fail_list',
            aggfunc='first'
        ).reset_index()

        # Rename vt_list to Offset
        final_pivot.rename(columns={'vt_list': 'Offset'}, inplace=True)

        return final_pivot

    except Exception as e:
        print(f"Error processing chunk data: {e}")
        return pd.DataFrame()

def load_data(input_dir):
    """Load and merge all CSV files starting with HS, only reading required columns"""
    # Define required columns
    needed_columns = ['SegmentName','Condition', 'Channel', 'Ce', 'Lun', 'Block', 'Page', 'Measurement', 'Value']

    csv_files = glob.glob(os.path.join(input_dir, 'HS*.csv'))
    if not csv_files:
        print("No matching CSV files found")
        return pd.DataFrame()

    all_data = []
    for file in csv_files:
        try:
            # First read file header to determine available columns
            header = pd.read_csv(file, nrows=0)
            usecols = [col for col in needed_columns if col in header.columns]

            # Only read required columns to reduce memory usage
            df = pd.read_csv(file, usecols=usecols, dtype=str, low_memory=False)
            all_data.append(df)
            print(f"Successfully read file {file}, {len(df)} rows")
        except Exception as e:
            print(f"Error reading file {file}: {e}")

    if not all_data:
        print("Failed to read any data")
        return pd.DataFrame()

    data = pd.concat(all_data, ignore_index=True)
    print(f"Data loading complete, {len(data)} rows total")
    return data

# Function to get region based on WordLine value
def get_wordline_region(wordline):
    """Return the corresponding region based on WordLine value"""
    for region, (start, end) in WORDLINE_REGIONS.items():
        if start <= wordline <= end:
            return region
    return -1  # Unknown region

def get_pec(block_value, pec_definitions, blk_group_config, blk_freq_config):
    """Get PEC name based on Block value and PEC definitions (including extension rules)"""
    if not isinstance(block_value, (int, float)) or pd.isna(block_value):
        return 'INVALID_BLOCK_TYPE'

    block_value = int(block_value) # Ensure integer comparison

    for pec_name, start_base in pec_definitions.items():
        for i in range(blk_group_config):
            current_start_block = start_base + i * blk_freq_config
            current_end_block = current_start_block + test_num_blk_per_pec - 1
            if current_start_block <= block_value <= current_end_block:
                return pec_name
    return 'OTHER_PEC'  # If no matching PEC found

def determine_block_group_index(block_value, base_blk_definitions, group_config, freq_config, num_blk_per_pec_config):
    """Determine the block group index (i) based on Block value"""
    if not isinstance(block_value, (int, float)) or pd.isna(block_value):
        return None

    block_value = int(block_value)

    for start_base in base_blk_definitions.values(): # test_base_blk values are now directly start_base
        for i in range(group_config):
            current_start_block = start_base + i * freq_config
            current_end_block = current_start_block + num_blk_per_pec_config - 1
            if current_start_block <= block_value <= current_end_block:
                return i # Return block group index
    return None # No matching block group index found

def get_partial_prog_ratio(row, rel_test_config, partial_prog_ratios_config):
    if "Partial" not in rel_test_config:
        return "100%"

    channel = row.get('Channel')
    ce = row.get('Ce')
    lun = row.get('Lun')
    block_group_idx = row.get('BlockGroupIndex')

    if channel is None or ce is None or lun is None:
        return "PARTIAL_PROG_KEY_COMPONENT_MISSING"

    # Ensure key components are strings
    key = f"{str(channel)}_{str(ce)}_{str(lun)}"

    # Get ratios_list corresponding to the key from partial_prog_ratios_config
    ratios_list = partial_prog_ratios_config.get(key)
    
    return ratios_list[block_group_idx]


def preprocess_data(data):
    """Data preprocessing: filter Measurement type, calculate WordLine and Level"""
    if data.empty:
        return data

    # 1. Filter Measurement type
    measurement_types = ['FAILS4CHUNK', 'vt_list', 'fail_list', 'BestDAC']
    data = data[data['Measurement'].isin(measurement_types)]

    # 2. Calculate WordLine/Level/WLmode (if Page column exists)
    if 'Page' in data.columns and not data.empty:
        data = data.copy()
        # Convert Page to numeric type
        data.loc[:, 'Page'] = pd.to_numeric(data['Page'], errors='coerce')
        # Calculate WordLine and Level
        if device_name == 'X36070':
            data.loc[:, 'WordLine'] = data['Page'].apply(lambda x: page_info(x)[3])
            data.loc[:, 'Level'] = data['Page'].apply(lambda x: page_info(x)[1])
            data.loc[:, 'WLmode'] = data['Page'].apply(lambda x: page_info(x)[4])
        else:
            data.loc[:, 'WordLine'] = data['Page'] // 3
            data.loc[:, 'Level'] = data['Page'] % 3
            data.loc[:, 'WLmode'] = 'TLC'
    else:
        print("Warning: 'Page' column does not exist")

    # 3. Add PEC column based on Block column
    if 'Block' in data.columns:
        data.loc[:, 'Block'] = pd.to_numeric(data['Block'], errors='coerce')
        data.loc[:, 'PEC'] = data['Block'].apply(lambda x: get_pec(x, test_base_blk, blk_group, blk_freq))
        print("PEC column added")

        # Add BlockGroupIndex column
        # test_base_blk, blk_group, blk_freq, test_num_blk_per_pec are imported from device_configure
        data.loc[:, 'BlockGroupIndex'] = data['Block'].apply(lambda x: determine_block_group_index(x, test_base_blk, blk_group, blk_freq, test_num_blk_per_pec))
        print("BlockGroupIndex column added")
    else:
        print("Warning: 'Block' column does not exist")

    # 4. Add Partial_Prog column based on Block column and Channel_Ce_Lun
    if 'Partial' in rel_test: # rel_test from device_configure
        # test_partial_prog_ratio from device_configure
        # get_partial_prog can now be obtained from row
        data.loc[:, 'Partial_Prog'] = data.apply(get_partial_prog_ratio, axis=1, rel_test_config=rel_test, partial_prog_ratios_config=test_partial_prog_ratio)
        print("Partial_Prog column added")
    else:
        data.loc[:, 'Partial_Prog'] = '100%'
        print("Partial_Prog column added (default 100%)")

    # 5. WordLine region divided by Wordline_Region
    data['WLGroup'] = data['WordLine'].apply(get_wordline_region)
    print("WLGroup column added")

    # 6. WLType divided
    # Get unique WordLine list corresponding to each test_partial_prog_ratio
    # If current WordLine + string_number is in unique WordLine list, then current Word Line belongs to current partial_prog's Inner WL, otherwise Edge WL
    for prog_ratio in data['Partial_Prog'].unique():
        if prog_ratio == '100%':
            data.loc[data['Partial_Prog'] == prog_ratio, 'WLType'] = "Close"
        else:
            unique_WL_list = data[data['Partial_Prog'] == prog_ratio]['WordLine'].unique()
            data.loc[data['Partial_Prog'] == prog_ratio, 'WLType'] = data['WordLine'].apply(lambda x: 'InnerWL' if int(x) + string_number in unique_WL_list else 'EdgeWL')
    print("WLType column added")

    # 7. Get stress from SegmentName
    data.loc[:, 'Stress'] = data['SegmentName'].str.split('_').str[0]
    print("Stress column added")

    data = data.drop(columns=['BlockGroupIndex'])

    print(f"Data preprocessing completed!")

    # Save preprocessed data
    save_results_to_csv(data, 'preprocessed_data', output_dir='output')

    return data

def process_fitting_data(data):
    """Process vt_list and fail_list data to generate fitting_data
    Use vectorized operations instead of loops to improve performance
    Memory optimized: delete intermediate variables to reduce memory usage
    """
    # Filter data - avoid copy() to save memory
    fitting_data = data[data['Measurement'].isin(['vt_list', 'fail_list'])]
    if fitting_data.empty:
        print("fitting_data is empty, no processing")
        return pd.DataFrame()

    try:
        # Check if required columns exist
        required_cols = ['Condition', 'Channel', 'Ce', 'Lun', 'Block', 'Page', 'WordLine', 'Level', 'WLmode', 'PEC', 'Partial_Prog', 'WLGroup', 'WLType', 'Measurement', 'Value']
        missing_cols = [col for col in required_cols if col not in fitting_data.columns]
        if missing_cols:
            print(f"fitting_data is missing required columns: {missing_cols}")
            raise KeyError(f"Missing required columns: {missing_cols}")

        # Use vectorized operations to process Value column
        # 1. Create value_array column containing split values
        fitting_data = fitting_data.copy()  # Only copy when necessary
        fitting_data['value_array'] = fitting_data['Value'].str.split('@')

        # Delete Value column to save memory
        fitting_data = fitting_data.drop(columns=['Value'])

        # 2. Use explode to expand data (vectorized operation)
        key_cols = ['Condition', 'Channel', 'Ce', 'Lun', 'Block', 'Page', 'WordLine', 'Level', 'WLmode', 'PEC', 'Partial_Prog', 'WLGroup', 'WLType', 'Measurement']
        # Expand value_array column lists into multiple rows, other columns remain unchanged
        exploded_data = fitting_data[key_cols + ['value_array']].explode('value_array')

        # Delete fitting_data to free memory
        del fitting_data
        gc.collect()

        # 3. Add index column and filter out empty values
        exploded_data['index'] = exploded_data.groupby(key_cols).cumcount()
        exploded_data = exploded_data[exploded_data['value_array'].notna() & (exploded_data['value_array'] != '')]
        exploded_data.rename(columns={'value_array': 'value'}, inplace=True)

        # 4. Pivot table conversion, vt_list and fail_list in measurement are treated as columns
        fitting_data_pivot = exploded_data.pivot_table(
            index=['Condition', 'Channel', 'Ce', 'Lun', 'Block', 'Page', 'WordLine', 'Level', 'WLmode', 'PEC', 'Partial_Prog', 'WLGroup', 'WLType', 'index'],
            columns='Measurement',
            values='value',
            aggfunc='first'
        ).reset_index()

        # Delete exploded_data to free memory
        del exploded_data
        gc.collect()

        # 5. Reset column names and convert to numeric type
        fitting_data_pivot.columns.name = None
        for col in ['vt_list', 'fail_list']:
            if col in fitting_data_pivot.columns:
                fitting_data_pivot[col] = pd.to_numeric(fitting_data_pivot[col], errors='coerce')

        # 6. Remove 'index' column
        fitting_data_pivot = fitting_data_pivot.drop(columns=['index'])

        # 7. Use pivot_table to expand fail_list by Condition
        final_result = fitting_data_pivot.pivot_table(
            index=['Channel', 'Ce', 'Lun', 'Block', 'WordLine', 'WLmode','PEC', 'Partial_Prog', 'WLGroup', 'WLType' ,'vt_list'],
            columns='Condition',
            values='fail_list',
            aggfunc='first'
        ).reset_index()

        # Delete intermediate pivot to free memory
        del fitting_data_pivot
        gc.collect()

        # Rename column as fail_list_{Condition value} format
        final_result.rename(columns={'vt_list': 'Offset'}, inplace=True)

        print(f"fitting_data processing completed, {len(final_result)} rows total")

        # Save fitting_data
        save_results_to_csv(final_result, 'fitting_data', output_dir='output')

        return final_result

    except Exception as e:
        print(f"Error processing fitting_data: {e}")
        return pd.DataFrame()

def process_fitting_data_optimized(data, chunk_size=None):
    """Memory-optimized version of process_fitting_data using chunked processing

    Args:
        data: Input DataFrame
        chunk_size: Number of unique Channel-Ce-Lun combinations to process at once
                   If None, auto-determine based on available memory
    """
    print("Starting memory-optimized fitting_data processing...")
    initial_memory = print_memory_info("initial state")

    # Filter data for vt_list and fail_list only
    fitting_data = data[data['Measurement'].isin(['vt_list', 'fail_list'])]
    if fitting_data.empty:
        print("fitting_data is empty, no processing")
        return pd.DataFrame()

    print_memory_info("after filtering data")

    try:
        # Check required columns
        required_cols = ['Condition', 'Channel', 'Ce', 'Lun', 'Block', 'Page',
                        'WordLine', 'Level', 'WLmode', 'PEC', 'Partial_Prog',
                        'WLGroup', 'WLType', 'Measurement', 'Value']
        missing_cols = [col for col in required_cols if col not in fitting_data.columns]
        if missing_cols:
            print(f"fitting_data is missing required columns: {missing_cols}")
            raise KeyError(f"Missing required columns: {missing_cols}")

        # Optimize data types before processing
        fitting_data = optimize_dataframe_dtypes(fitting_data)
        print_memory_info("after data type optimization")

        # Get unique Channel-Ce-Lun combinations for chunking
        group_cols = ['Channel', 'Ce', 'Lun']
        unique_groups = fitting_data[group_cols].drop_duplicates()
        total_groups = len(unique_groups)

        # Auto-determine chunk size if not provided
        if chunk_size is None:
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
            # Estimate chunk size based on available memory (conservative approach)
            chunk_size = max(1, min(10, int(available_memory_gb * 2)))

        print(f"Processing {total_groups} unique Channel-Ce-Lun groups in chunks of {chunk_size}")

        # Process data in chunks
        all_results = []

        for i in range(0, total_groups, chunk_size):
            chunk_groups = unique_groups.iloc[i:i+chunk_size]
            chunk_start = i + 1
            chunk_end = min(i + chunk_size, total_groups)

            print(f"Processing chunk {chunk_start}-{chunk_end}/{total_groups}...")

            # Create filter for current chunk
            chunk_filter = pd.Series(False, index=fitting_data.index)
            for _, group in chunk_groups.iterrows():
                group_filter = (
                    (fitting_data['Channel'] == group['Channel']) &
                    (fitting_data['Ce'] == group['Ce']) &
                    (fitting_data['Lun'] == group['Lun'])
                )
                chunk_filter |= group_filter

            # Process current chunk
            chunk_data = fitting_data[chunk_filter].copy()
            chunk_result = process_chunk_data(chunk_data)

            if not chunk_result.empty:
                all_results.append(chunk_result)

            # Clear chunk data and force garbage collection
            del chunk_data, chunk_filter
            clear_memory()

            print_memory_info(f"chunk {chunk_start}-{chunk_end}")

        # Combine all results
        if all_results:
            print("Combining all chunk results...")
            final_result = pd.concat(all_results, ignore_index=True)

            # Clear intermediate results
            del all_results
            clear_memory()

            print_memory_info("after combining results")
            print(f"fitting_data processing completed, {len(final_result)} rows total")

            # Save results
            save_results_to_csv(final_result, 'fitting_data', output_dir='output')

            final_memory = print_memory_info("final state")
            memory_saved = initial_memory - final_memory
            print(f"Memory efficiency: {memory_saved:+.1f} MB change from initial state")

            return final_result
        else:
            print("No valid results generated")
            return pd.DataFrame()

    except Exception as e:
        print(f"Error in optimized fitting_data processing: {e}")
        return pd.DataFrame()

def process_fitting_data_with_options(data, use_optimized=True, chunk_size=None, memory_threshold_mb=1000):
    """Wrapper function to choose between original and optimized processing

    Args:
        data: Input DataFrame
        use_optimized: Whether to use the memory-optimized version
        chunk_size: Chunk size for optimized processing (auto-determined if None)
        memory_threshold_mb: Memory threshold to auto-switch to optimized version
    """
    current_memory = get_memory_usage()
    data_size_mb = data.memory_usage(deep=True).sum() / 1024 / 1024

    print(f"Current memory usage: {current_memory:.1f} MB")
    print(f"Input data size: {data_size_mb:.1f} MB")

    # Auto-switch to optimized version if memory usage is high
    if not use_optimized and (current_memory > memory_threshold_mb or data_size_mb > memory_threshold_mb / 2):
        print(f"Auto-switching to optimized version due to high memory usage")
        use_optimized = True

    if use_optimized:
        print("Using memory-optimized processing...")
        return process_fitting_data_optimized(data, chunk_size)
    else:
        print("Using original processing...")
        return process_fitting_data(data)

def process_read_data(data):
    """Process FAILS4CHUNK data to generate read_data
    Simplify processing flow, use vectorized operations
    """
    # Filter data
    read_data = data[data['Measurement'] == 'FAILS4CHUNK'].copy()
    if read_data.empty:
        print("read_data is empty, no processing")
        return pd.DataFrame()

    try:
        # Define columns to keep
        cols_to_keep = ['Channel', 'Ce', 'Lun', 'Block', 'WordLine', 'Level', 'WLmode' ,'Value']
        available_cols = [col for col in cols_to_keep if col in read_data.columns]

        # Only keep required columns to reduce memory usage
        read_data = read_data[available_cols]

        # Split Value by @ to expand into multiple rows (use vectorized operations)
        read_data['Value'] = read_data['Value'].str.split('@')
        read_data = read_data.explode('Value')

        # Remove rows with empty Value
        read_data = read_data[read_data['Value'].notna() & (read_data['Value'].str.strip() != '')]

        # Count occurrences of same value
        group_cols = [col for col in available_cols if col != 'Value']
        read_data = read_data.groupby(group_cols + ['Value']).size().reset_index(name='Counter')

        print(f"read_data processing completed, {len(read_data)} rows total")

        # Save read_data
        save_results_to_csv(read_data, 'read_data', output_dir='output')

    except Exception as e:
        print(f"Error processing read_data: {e}")

def process_bestoffset(data):
    """Process BestDAC data to generate bestoffset
    Optimize data processing flow, use vectorized operations
    """
    # Filter data
    bestoffset = data[data['Measurement'] == 'BestDAC'].copy()
    if bestoffset.empty:
        print("bestoffset is empty, no processing")
        return pd.DataFrame()

    try:
        # Split Value into multiple columns and remove trailing empty values
        if 'Value' in bestoffset.columns and not bestoffset['Value'].isnull().all():

            # Use vectorized operations to process Value column
            # 1. Split string
            split_values = bestoffset['Value'].str.split('@', expand=True)

            # 2. Create state column names
            state_cols = [f'State{i+1}' for i in range(split_values.shape[1])]
            split_values.columns = state_cols[:split_values.shape[1]]

            # 3. Remove trailing empty value columns
            # Check non-empty value ratio of each column
            non_empty_ratio = split_values.notna().mean()
            # Keep columns with non-empty value ratio greater than threshold
            valid_cols = non_empty_ratio[non_empty_ratio > 0.1].index.tolist()
            split_values = split_values[valid_cols]

            # 4. Convert to numeric type and handle values greater than 127
            for col in split_values.columns:
                # Convert to numeric
                split_values[col] = pd.to_numeric(split_values[col], errors='coerce')
                # Handle values greater than 127
                mask = split_values[col] > 127
                split_values.loc[mask, col] = split_values.loc[mask, col] - 256

            # 5. Merge back to bestoffset
            bestoffset = pd.concat([bestoffset, split_values], axis=1)

        # Remove unnecessary columns
        columns_to_remove = ['Condition', 'Measurement', 'Value']
        bestoffset = bestoffset.drop(columns=[col for col in columns_to_remove if col in bestoffset.columns])

        print(f"bestoffset processing completed, {len(bestoffset)} rows total")

        # Save bestoffset
        save_results_to_csv(bestoffset, 'bestoffset', output_dir='output')

        return bestoffset

    except Exception as e:
        print(f"Error processing bestoffset: {e}")
        return pd.DataFrame()

def save_results_to_csv(data, framename, output_dir='.'):
    """Save processing results as CSV file

    Args:
        fitting_data_final: Processed fitting_data dataframe
        read_data: Processed read_data dataframe
        bestoffset: Processed bestoffset dataframe
        cluster_results: List of clustering results
        output_dir: Output directory, default current directory
    """
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    # Define common save function
    def save_dataframe(df, filename, description):
        if df is None:
            print(f'{description} is empty, no output file')
            return

        filepath = os.path.join(output_dir, filename)
        try:
            df.to_csv(filepath, index=False, encoding='utf-8')
            print(f'{description} output to {filepath}')
        except Exception as e:
            print(f'Error saving {description}: {e}')

    # Save various data
    save_dataframe(data, f'{framename}.csv', f'{framename}')